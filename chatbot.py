# -*- coding: utf-8 -*-
"""ChatBot.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/16JFe3SPh3DtE6NbRq6n67O3oeDcmtumq

**Importing neccesary libraries**
"""

import numpy as np
import nltk
import string
import random

"""**Loading the corpus and performing pre-processing on it (tokenization,lower casing,etc\)**"""

f = open('chatbot.txt','r',errors = 'ignore')
#reading document of corpus in raw format
doc_raw = f.read()
#converting every alphabet to lowercase
doc_raw = doc_raw.lower()
# Dowloading the pre-trained tokenizer
nltk.download('punkt')
#Using the wordnet dictionary
nltk.download('wordnet')
#splitting document into sentences
sent_tok = nltk.sent_tokenize(doc_raw)
#splitting document into words
words_tok = nltk.word_tokenize(doc_raw)

sent_tok[:2]

words_tok[:2]

"""**Performing data pre-processing techniques (e.g.removing punctuations,lemmetization,etc)**"""

lemmer = nltk.stem.WordNetLemmatizer()
def lem_tokens(tokens):
  return [lemmer.lemmatize(token) for token in tokens]
rem_punct_dict = dict((ord(punct),None) for punct in string.punctuation)
def LemNorm(text):
  return lem_tokens(nltk.word_tokenize(text.lower().translate(rem_punct_dict)))

"""**Creating a basic greeting function**"""

Greet_inputs = ('hello','hi',"what's up?","sup","hey","greetings")
Greet_response = ("hi","hey","nods","hi there","hello","Glad to know you are talking to me!")
def greet(sentence):
  for word in sentence.split():
    if word.lower() in Greet_inputs:
      return random.choice(Greet_response)

"""**Generating Response to Input Texts**"""

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

def response(user_response):
  robot_res = ''
  TfidfVector = TfidfVectorizer(tokenizer = LemNorm, stop_words='english')
  tfidf = TfidfVector.fit_transform(sent_tok)#Convert a collection of raw documents to a matrix of TF-IDF features.
  vals = cosine_similarity(tfidf[-1],tfidf)
  idx = vals.argsort()[0][-2]
  flat = vals.flatten()
  flat.sort()
  req_tfidf = flat[-2]
  if(req_tfidf==0):
    robot_res = robot_res+'I am sorry! I do not understand you'
    return robot_res
  else:
    robot_res = robot_res+sent_tok[idx]
    return robot_res

"""**Defining conversation start and end protocols**"""

flag = True
print("BOT: Hi! my name is Alexa. Let's start a conversation!Also if you want to exit at any time,just type bye.")
while (flag == True):
  user_response = input()
  user_response = user_response.lower()
  if (user_response != 'bye'):
    if (user_response == 'thanks'or user_response =='thank you'):
      flag = False
      print('BOT: You are welcome!..')
    else:
      if(greet(user_response)!=None):
        print('BOT:'+greet(user_response))
      else:
        sent_tok.append(user_response)
        words_tok = words_tok +nltk.word_tokenize(user_response)
        final_words =list(set(words_tok))
        print('BOT:',end="")
        print(response(user_response))
        sent_tok.remove(user_response)
  else:
    flag = False
    print("BOT: Goodbye take care <3")

