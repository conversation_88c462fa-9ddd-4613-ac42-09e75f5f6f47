class NumberProcessor:
    """
    A class for processing and comparing numbers with various mathematical operations.
    """

    def __init__(self, *initial_numbers):
        """
        Initialize the NumberProcessor with optional initial numbers.

        Args:
            *initial_numbers: Variable number of initial numeric values
        """
        self.numbers = list(initial_numbers) if initial_numbers else []

    def add_number(self, number):
        """
        Add a single number to the processor.

        Args:
            number: Numeric value to add
        """
        self.numbers.append(number)

    def add_numbers(self, *numbers):
        """
        Add multiple numbers to the processor.

        Args:
            *numbers: Variable number of numeric values to add
        """
        self.numbers.extend(numbers)

    def compute_sum(self, *additional_numbers):
        """
        Compute the sum of stored numbers and any additional numbers.

        Args:
            *additional_numbers: Optional additional numbers to include in sum

        Returns:
            The sum of all numbers
        """
        all_numbers = self.numbers + list(additional_numbers)
        return sum(all_numbers)

    def compare_two_numbers(self, num1, num2):
        """
        Compare two numbers and return the comparison result.

        Args:
            num1: First number to compare
            num2: Second number to compare

        Returns:
            str: Comparison result message
        """
        if num2 > num1:
            return f"{num2} is bigger than {num1}"
        elif num1 > num2:
            return f"{num1} is bigger than {num2}"
        else:
            return f"{num1} and {num2} are equal"

    def find_max(self):
        """
        Find the maximum number among stored numbers.

        Returns:
            The maximum number, or None if no numbers are stored
        """
        return max(self.numbers) if self.numbers else None

    def find_min(self):
        """
        Find the minimum number among stored numbers.

        Returns:
            The minimum number, or None if no numbers are stored
        """
        return min(self.numbers) if self.numbers else None

    def get_average(self):
        """
        Calculate the average of stored numbers.

        Returns:
            The average value, or None if no numbers are stored
        """
        return sum(self.numbers) / len(self.numbers) if self.numbers else None

    def get_numbers(self):
        """
        Get a copy of the stored numbers.

        Returns:
            list: Copy of stored numbers
        """
        return self.numbers.copy()

    def clear_numbers(self):
        """
        Clear all stored numbers.
        """
        self.numbers.clear()

    def __str__(self):
        """
        String representation of the NumberProcessor.

        Returns:
            str: String representation showing stored numbers
        """
        return f"NumberProcessor(numbers={self.numbers})"

    def __repr__(self):
        """
        Official string representation of the NumberProcessor.

        Returns:
            str: Representation that can recreate the object
        """
        return f"NumberProcessor({', '.join(map(str, self.numbers))})"


def main():
    """
    Main function demonstrating the NumberProcessor class usage.
    """
    # Create a NumberProcessor instance with initial values
    processor = NumberProcessor(33, 200)

    # Demonstrate comparison functionality
    a, b = 33, 200
    comparison_result = processor.compare_two_numbers(a, b)
    print(comparison_result)

    # Demonstrate sum computation
    sum_result = processor.compute_sum()
    print(f"Sum of stored numbers: {sum_result}")

    # Add more numbers and compute sum
    processor.add_numbers(1, 2, 3, 4, 5)
    total_sum = processor.compute_sum()
    print(f"Sum after adding more numbers: {total_sum}")

    # Demonstrate additional functionality
    print(f"Maximum number: {processor.find_max()}")
    print(f"Minimum number: {processor.find_min()}")
    print(f"Average: {processor.get_average():.2f}")

    # Demonstrate computing sum with additional numbers
    sum_with_extra = processor.compute_sum(10, 20, 30)
    print(f"Sum including extra numbers (10, 20, 30): {sum_with_extra}")

    print(f"Processor state: {processor}")


if __name__ == "__main__":
    main()