def max_dd(initial_investment):
  Profits = calc_profit(d)
  cum_sum = sum(Profits)
  Equity = cum_sum + initial_investment
  Peak = Equity.cummax()
  drawdown = ((Equity - Peak)/Peak)*100
  max_dd = drawdown.min()
  return max_dd

def ROI(initial_investment):
  Profits = calc_profit(d)
  ROI = ((sum(Profits)+initial_investment)-initial_investment//initial_investment)*100
  return ROI

def avg_gain_loss(d):
  Profits = calc_profit(d)
  # avg_gain = np.mean([i for i in Profits if i>0])
  # avg_loss = np.mean([i for i in Profits if i<0])
  gain = [i for i in Profits if i > 0]
  loss = [i for i in Profits if i <0]
  avg_gain = sum(Profits)/len(Profits)
  avg_loss = sum(loss)/len(Profits)
  return avg_gain,avg_loss

def calc_profit(d):
  Profits = []
  buy=[]

  for i in range(len(d)):
    if d.Action.iloc[i] == 'buy':
      buy.append(d.Ticker_Price.iloc[i])
    elif d.Action.iloc[i] == 'sell':
      profit = (d.Ticker_Price.iloc[i]*len(buy))-sum(buy)
      buy.clear()
      Profits.append(profit)
  return Profits

def win_rate(d):
  Profits=calc_profit(d)
  wins = [i for i in Profits if i>0]
  win_rate = (len(wins)/len(Profits))*100
  return win_rate
  
print(win_rate(d))



Timestamp = df.index[0:7]
Timestamp
Action=['buy','buy','buy','sell','buy','buy','sell']
Price=[20,24,23,30,22,21,28]
Stocks_quant = [1,2,1,4,1,1,2]
d = pd.DataFrame({'Action':Action,'Ticker_Price':Price,'Stocks_quant':Stocks_quant},index=Timestamp)
d