To solve this problem, we need to find the maximum sum of a subarray with the constraint that no two elements in the subarray are adjacent. This is a variation of the "House Robber" problem in dynamic programming.

Here's a detailed plan:

1. **Initialization**: We'll maintain two arrays (or lists) to keep track of:
   - The maximum sum we can achieve up to each index, while including the element at that index (`include`).
   - The maximum sum we can achieve up to each index, while excluding the element at that index (`exclude`).

2. **Transition**:
   - If we include the current element in the sum, the previous element must be excluded.
   - If we exclude the current element, the maximum sum up to this point is the maximum of the sum including or excluding the previous element.

3. **Reconstruction**:
   - To reconstruct the subarray that gives the maximum sum, we'll backtrack through our arrays to see which elements were included in the optimal solution.

Here's the Python function to achieve this:

```python
def max_sum(arr):
    if not arr:
        return (0, [])
    if len(arr) == 1:
        return (arr[0], [arr[0]])
    
    n = len(arr)
    include = [0] * n
    exclude = [0] * n
    
    include[0] = arr[0]
    exclude[0] = 0
    
    for i in range(1, n):
        include[i] = exclude[i-1] + arr[i]
        exclude[i] = max(include[i-1], exclude[i-1])
    
    max_sum = max(include[n-1], exclude[n-1])
    
    # Reconstruct the subarray
    subarray = []
    i = n - 1
    while i >= 0:
        if include[i] > exclude[i]:
            subarray.append(arr[i])
            i -= 2  # Move two steps back
        else:
            i -= 1  # Move one step back
    
    subarray.reverse()  # Since we collected the elements in reverse order
    
    return (max_sum, subarray)

# Example usage:
arr = [2, 7, 9, 3, 1]
print(max_sum(arr))  # Output: (12, [2, 9, 1])
```

### Explanation:

1. **Base Cases**:
   - If the array is empty, the result is `(0, [])`.
   - If the array has one element, the result is that element itself.

2. **Dynamic Programming Arrays**:
   - `include[i]` stores the maximum sum we can get including the `i`-th element.
   - `exclude[i]` stores the maximum sum we can get excluding the `i`-th element.

3. **Transition Logic**:
   - `include[i] = exclude[i-1] + arr[i]`: If we include `arr[i]`, we must exclude `arr[i-1]`.
   - `exclude[i] = max(include[i-1], exclude[i-1])`: If we exclude `arr[i]`, we take the maximum sum up to the previous element.

4. **Reconstruction**:
   - We backtrack from the end of the arrays to construct the subarray that resulted in the maximum sum.
   - We append elements to the subarray if they were included in the optimal solution and then move back by two indices to ensure no adjacent elements are included.

This function should efficiently compute the desired maximum subarray sum with non-adjacent elements.